<?php
/**
 * 通过类重复定义错误获取完整源码
 * 捕获PHP执行过程中的内存状态，从Fatal Error中提取解密后的代码
 */

// 设置错误处理
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 自定义错误处理函数
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    // 检查是否是类重复定义错误
    if (strpos($errstr, 'Cannot redeclare class') !== false) {
        echo "=== 捕获到类重复定义错误 ===\n";
        echo "错误信息: $errstr\n";
        echo "文件: $errfile\n";
        echo "行号: $errline\n";
        
        // 尝试获取当前内存中的类定义
        $declaredClasses = get_declared_classes();
        echo "\n=== 当前已声明的类 ===\n";
        foreach ($declaredClasses as $class) {
            if (strpos($class, 'core\\') === 0 || strpos($class, 'Kernel') !== false) {
                echo "类名: $class\n";
                
                // 尝试获取类的反射信息
                try {
                    $reflection = new ReflectionClass($class);
                    echo "文件: " . $reflection->getFileName() . "\n";
                    echo "起始行: " . $reflection->getStartLine() . "\n";
                    echo "结束行: " . $reflection->getEndLine() . "\n";
                    
                    // 获取类的源码
                    if ($reflection->getFileName()) {
                        $source = file_get_contents($reflection->getFileName());
                        $lines = explode("\n", $source);
                        $classSource = array_slice($lines, $reflection->getStartLine() - 1, 
                                                 $reflection->getEndLine() - $reflection->getStartLine() + 1);
                        
                        echo "\n=== 类源码 ===\n";
                        echo implode("\n", $classSource);
                        echo "\n\n";
                    }
                } catch (Exception $e) {
                    echo "反射错误: " . $e->getMessage() . "\n";
                }
            }
        }
        
        // 输出调用栈
        echo "\n=== 调用栈 ===\n";
        debug_print_backtrace();
        
        return true; // 阻止默认错误处理
    }
    
    return false; // 让其他错误正常处理
}

// 设置自定义错误处理器
set_error_handler('customErrorHandler');

// 输出缓冲区处理
function captureOutput() {
    $output = ob_get_contents();
    if ($output) {
        echo "\n=== 捕获的输出 ===\n";
        echo $output;
        echo "\n==================\n";
    }
}

// 注册关闭函数
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && $error['type'] === E_ERROR) {
        echo "\n=== Fatal Error 信息 ===\n";
        echo "错误: " . $error['message'] . "\n";
        echo "文件: " . $error['file'] . "\n";
        echo "行号: " . $error['line'] . "\n";
        
        // 尝试从错误信息中提取有用信息
        if (strpos($error['message'], 'Cannot redeclare class') !== false) {
            echo "\n检测到类重复定义错误，这可能包含解密后的代码信息\n";
        }
    }
    
    captureOutput();
});

// 开始输出缓冲
ob_start();

echo "=== 开始解密分析 ===\n";

// 首先尝试直接包含原始文件
try {
    echo "尝试包含原始 Kernel.php 文件...\n";
    include_once 'core/basic/Kernel.php';
    echo "成功包含文件\n";
} catch (Exception $e) {
    echo "包含文件时发生异常: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "包含文件时发生错误: " . $e->getMessage() . "\n";
}

// 尝试再次包含以触发重复定义错误
try {
    echo "\n尝试再次包含以触发重复定义错误...\n";
    include 'core/basic/Kernel.php';
} catch (Exception $e) {
    echo "第二次包含时发生异常: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "第二次包含时发生错误: " . $e->getMessage() . "\n";
}

// 检查是否有Kernel类被定义
if (class_exists('core\\basic\\Kernel')) {
    echo "\n=== Kernel 类已定义 ===\n";
    $reflection = new ReflectionClass('core\\basic\\Kernel');
    
    echo "类名: " . $reflection->getName() . "\n";
    echo "文件: " . $reflection->getFileName() . "\n";
    
    // 获取所有方法
    $methods = $reflection->getMethods();
    echo "\n=== 类方法 ===\n";
    foreach ($methods as $method) {
        echo "方法: " . $method->getName() . "\n";
        echo "  可见性: " . ($method->isPublic() ? 'public' : ($method->isProtected() ? 'protected' : 'private')) . "\n";
        echo "  静态: " . ($method->isStatic() ? 'yes' : 'no') . "\n";
        echo "  起始行: " . $method->getStartLine() . "\n";
        echo "  结束行: " . $method->getEndLine() . "\n";
    }
    
    // 获取所有属性
    $properties = $reflection->getProperties();
    echo "\n=== 类属性 ===\n";
    foreach ($properties as $property) {
        echo "属性: " . $property->getName() . "\n";
        echo "  可见性: " . ($property->isPublic() ? 'public' : ($property->isProtected() ? 'protected' : 'private')) . "\n";
        echo "  静态: " . ($property->isStatic() ? 'yes' : 'no') . "\n";
    }
}

echo "\n=== 分析完成 ===\n";

// 结束输出缓冲
ob_end_flush();
?>
