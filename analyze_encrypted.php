<?php
/**
 * 分析加密的Kernel.php文件
 * 提取并解密base64编码的内容
 */

echo "=== 分析加密的 Kernel.php 文件 ===\n\n";

// 读取原始文件内容
$kernelFile = 'core/basic/Kernel.php';
if (!file_exists($kernelFile)) {
    die("错误: 找不到文件 $kernelFile\n");
}

$content = file_get_contents($kernelFile);
echo "文件大小: " . strlen($content) . " 字节\n";

// 分析文件结构
echo "\n=== 文件结构分析 ===\n";

// 查找变量定义
preg_match_all('/\$([^=]+)=/', $content, $variables);
if (!empty($variables[1])) {
    echo "发现变量:\n";
    foreach ($variables[1] as $var) {
        echo "  \$$var\n";
    }
}

// 查找base64编码的字符串
preg_match_all('/[A-Za-z0-9+\/]{50,}={0,2}/', $content, $base64Strings);
if (!empty($base64Strings[0])) {
    echo "\n发现可能的base64编码字符串:\n";
    foreach ($base64Strings[0] as $i => $str) {
        echo "字符串 " . ($i + 1) . " (长度: " . strlen($str) . "): " . substr($str, 0, 50) . "...\n";
        
        // 尝试解码
        $decoded = base64_decode($str, true);
        if ($decoded !== false) {
            echo "  解码成功，长度: " . strlen($decoded) . "\n";
            echo "  开头内容: " . substr($decoded, 0, 100) . "\n";
            
            // 检查是否包含PHP代码
            if (strpos($decoded, '<?php') !== false || strpos($decoded, 'class') !== false) {
                echo "  *** 可能包含PHP代码 ***\n";
                
                // 保存解码内容到文件
                $outputFile = "decoded_content_$i.php";
                file_put_contents($outputFile, $decoded);
                echo "  已保存到: $outputFile\n";
            }
        } else {
            echo "  解码失败\n";
        }
        echo "\n";
    }
}

// 查找eval函数调用
echo "\n=== 查找eval调用 ===\n";
if (strpos($content, 'eval(') !== false) {
    echo "发现eval函数调用\n";
    
    // 提取eval参数
    preg_match('/eval\(([^)]+)\)/', $content, $evalMatch);
    if (!empty($evalMatch[1])) {
        echo "eval参数: " . $evalMatch[1] . "\n";
        
        // 分析参数结构
        $evalParam = $evalMatch[1];
        echo "参数分析:\n";
        
        // 查找函数调用模式
        if (preg_match('/\$([^(]+)\(/', $evalParam, $funcMatch)) {
            echo "  发现函数调用: \$" . $funcMatch[1] . "\n";
        }
        
        // 查找字符串参数
        if (preg_match('/"([^"]+)"/', $evalParam, $stringMatch)) {
            echo "  发现字符串参数: " . substr($stringMatch[1], 0, 100) . "...\n";
            
            // 尝试base64解码字符串参数
            $decoded = base64_decode($stringMatch[1], true);
            if ($decoded !== false) {
                echo "  字符串参数base64解码成功，长度: " . strlen($decoded) . "\n";
                echo "  解码内容开头: " . substr($decoded, 0, 200) . "\n";
                
                // 保存解码的字符串参数
                file_put_contents('decoded_eval_param.txt', $decoded);
                echo "  已保存解码内容到: decoded_eval_param.txt\n";
            }
        }
    }
}

// 尝试模拟解密过程
echo "\n=== 尝试模拟解密 ===\n";

// 提取关键变量
$lines = explode("\n", $content);
foreach ($lines as $line) {
    $line = trim($line);
    if (strpos($line, '$') === 0 && strpos($line, '=') !== false) {
        echo "变量定义: $line\n";
        
        // 尝试执行变量定义（安全起见，只处理简单的字符串赋值）
        if (preg_match('/\$([^=]+)=\'([^\']+)\';/', $line, $match)) {
            $varName = trim($match[1]);
            $varValue = $match[2];
            echo "  变量名: $varName\n";
            echo "  变量值长度: " . strlen($varValue) . "\n";
            echo "  变量值开头: " . substr($varValue, 0, 50) . "...\n";
        }
    }
}

echo "\n=== 分析完成 ===\n";
echo "请检查生成的解码文件以获取更多信息。\n";
?>
