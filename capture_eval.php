<?php
/**
 * 捕获eval执行过程中的解密内容
 * 通过重写eval函数来拦截解密后的代码
 */

echo "=== 捕获eval执行内容 ===\n\n";

// 创建一个临时文件来存储解密后的内容
$decryptedFile = 'decrypted_kernel_source.php';

// 读取原始加密文件
$kernelFile = 'core/basic/Kernel.php';
$originalContent = file_get_contents($kernelFile);

echo "原始文件大小: " . strlen($originalContent) . " 字节\n";

// 提取eval调用中的base64字符串
preg_match('/eval\([^"]*"([^"]+)"/', $originalContent, $matches);
if (empty($matches[1])) {
    die("错误: 无法找到base64编码的字符串\n");
}

$base64String = $matches[1];
echo "找到base64字符串，长度: " . strlen($base64String) . "\n";

// 解码base64字符串
$decodedContent = base64_decode($base64String, true);
if ($decodedContent === false) {
    die("错误: base64解码失败\n");
}

echo "base64解码成功，长度: " . strlen($decodedContent) . "\n";
echo "解码内容开头: " . substr($decodedContent, 0, 200) . "\n\n";

// 保存解码后的内容
file_put_contents('raw_decoded.txt', $decodedContent);
echo "原始解码内容已保存到: raw_decoded.txt\n";

// 分析解码后的内容结构
echo "\n=== 分析解码内容 ===\n";

// 检查是否包含PHP代码特征
$phpIndicators = ['<?php', 'class ', 'function ', 'namespace ', 'use '];
foreach ($phpIndicators as $indicator) {
    if (strpos($decodedContent, $indicator) !== false) {
        echo "发现PHP代码特征: $indicator\n";
    }
}

// 查找可能的进一步编码
if (preg_match_all('/[A-Za-z0-9+\/]{100,}={0,2}/', $decodedContent, $innerBase64)) {
    echo "\n发现内层可能的base64编码:\n";
    foreach ($innerBase64[0] as $i => $str) {
        echo "内层字符串 " . ($i + 1) . " (长度: " . strlen($str) . ")\n";
        
        $innerDecoded = base64_decode($str, true);
        if ($innerDecoded !== false) {
            echo "  内层解码成功，长度: " . strlen($innerDecoded) . "\n";
            echo "  内容开头: " . substr($innerDecoded, 0, 100) . "\n";
            
            // 保存内层解码内容
            file_put_contents("inner_decoded_$i.txt", $innerDecoded);
            echo "  已保存到: inner_decoded_$i.txt\n";
        }
        echo "\n";
    }
}

// 尝试执行解码后的内容（在受控环境中）
echo "\n=== 尝试安全执行解码内容 ===\n";

// 创建一个修改版本的代码，替换eval为我们的捕获函数
function captureEval($code) {
    global $decryptedFile;
    
    echo "捕获到eval执行的代码，长度: " . strlen($code) . "\n";
    echo "代码开头: " . substr($code, 0, 200) . "\n";
    
    // 保存捕获的代码
    file_put_contents($decryptedFile, "<?php\n" . $code);
    echo "解密后的代码已保存到: $decryptedFile\n";
    
    return $code;
}

// 尝试模拟原始文件中的解密过程
echo "\n=== 模拟解密过程 ===\n";

// 从原始文件中提取变量定义
$lines = explode("\n", $originalContent);
$variables = [];

foreach ($lines as $line) {
    $line = trim($line);
    if (preg_match('/\$([^=]+)=([^;]+);/', $line, $match)) {
        $varName = trim($match[1]);
        $varValue = trim($match[2], '"\'');
        
        echo "发现变量: \$$varName\n";
        
        // 如果是字符串变量，存储它
        if (preg_match('/^[\'"].*[\'"]$/', trim($match[2]))) {
            $variables[$varName] = trim($match[2], '"\'');
            echo "  存储字符串变量，长度: " . strlen($variables[$varName]) . "\n";
        }
    }
}

// 查找字符访问模式 (如 $var[123])
if (preg_match_all('/\$([^[]+)\[(\d+)\]/', $originalContent, $accessMatches, PREG_SET_ORDER)) {
    echo "\n发现字符访问模式:\n";
    foreach ($accessMatches as $match) {
        $varName = $match[1];
        $index = (int)$match[2];
        
        if (isset($variables[$varName]) && $index < strlen($variables[$varName])) {
            $char = $variables[$varName][$index];
            echo "  \${$varName}[$index] = '$char'\n";
        }
    }
}

echo "\n=== 处理完成 ===\n";
echo "请检查生成的文件以获取解密后的源码。\n";
?>
